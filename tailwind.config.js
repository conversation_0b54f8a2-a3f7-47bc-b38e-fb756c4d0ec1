import tailwindScrollbar from 'tailwind-scrollbar';
import layouts from './plugins/layouts/layouts';
import sidebar from './plugins/layouts/sidebar';
import buttons from './plugins/buttons';
import tables from './plugins/tables';
import flatpicker from './plugins/plugins/flatpicker';
import apexchart from './plugins/plugins/apexchart';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './index.html',
    './src/**/*.{js,jsx,ts,tsx}',
    './node_modules/apexcharts/**/*',
  ],

  darkMode: ['class', '[data-mode="dark"]'],
  theme: {
    container: {
      center: true,
      padding: '1.5rem',
    },
    extend: {
      fontFamily: {
        pretendard: ['Pretendard'],
        'bruno-ace': ['BrunoAce'],
      },
      colors: {
        primary: {
          '0-1': '#FAFAFB',
          0: '#F0F0F4',
          1: '#EBEDFF',
          2: '#C3C5DF',
          3: '#AAADCA',
          4: '#9093B5',
          5: '#787BA2',
          6: '#585B88',
          7: '#404375',
          8: '#282C62',
          9: '#10144E',
          10: '#010542',
        },
        secondary: {
          0: '#FFF2EB',
          1: '#FFDECC',
          2: '#FFBE9A',
          3: '#FFA06D',
          4: '#FF8341',
          5: '#FF6817',
          6: '#FF5900',
          7: '#DF4E00',
          8: '#C84601',
          9: '#FB33F01',
          10: '#AA3C01',
        },
        gray: {
          1: '#FBFBFB',
          2: '#F7F7F7',
          3: '#F5F4F3',
          4: '#EFEFEF',
          5: '#ECECEC',
          6: '#DFDFDF',
          7: '#C1C1C1',
          8: '#A5A5A5',
          9: '#8B8B8B',
          10: '#6F6F6F',
          11: '#555555',
          12: '#3D3D3D',
          13: '#333333',
          14: '#242424',
          15: '#171717',
        },
        semantic: {
          1: '#38AA51',
          2: '#1C76E0',
          3: '#EAB522',
          4: '#EF3823',
          '1-1': '#EBF7EE',
          '2-1': '#E8F1FC',
          '3-1': '#FDF8E9',
          '4-1': '#FDEBE9',
        },
        calender: {
          r: '#F23B3B',
          o: '#FF6A33',
          y: '#FCD828',
          g: '#E39933',
          b: '#3864FC',
          p: '#893399',
          gre: '#8B8B8B',
          'r-1': '#FDDEDE',
          'o-1': '#FFE5DA',
          'y-1': '#FFF7D1',
          'g-1': '#DEEFDE',
          'b-1': '#E1E8FE',
          'p-1': '#EADCED',
          'gre-1': '#EFEFEF',
        },
      },
      backgroundImage: {
        gradient1:
          'linear-gradient(90deg, var(--Primary-Pri-1, #003292) 0%, #00CB88 100%)',
        'gradient1-p3':
          'linear-gradient(90deg, var(--Primary-Pri-1, color(display-p3 0 0.1882 0.5294)) 0%, color(display-p3 0.3569 0.7843 0.5529) 100%)',
        gradient2:
          'linear-gradient(90deg, var(--Primary-Pri-1, #003292) 0%, #C89BFF 100%)',
        'gradient2-p3':
          'linear-gradient(90deg, var(--Primary-Pri-1, color(display-p3 0 0.1882 0.5294)) 0%, color(display-p3 0.7569 0.6157 1) 100%)',
        gradient3:
          'linear-gradient(90deg, var(--Primary-Pri-1, #003292) 0%, #5184EA 100%)',
        'gradient3-p3':
          'linear-gradient(90deg, var(--Primary-Pri-1, color(display-p3 0 0.1882 0.5294)) 0%, color(display-p3 0.3647 0.5137 0.8902) 100%)',
        gradient4:
          'linear-gradient(90deg, var(--Primary-Pri-1, #003292) 0%, #FF005D 100%)',
        'gradient4-p3':
          'linear-gradient(90deg, var(--Primary-Pri-1, color(display-p3 0 0.1882 0.5294)) 0%, color(display-p3 1 0.1765 0.3804) 100%)',
        gradient5:
          'linear-gradient(90deg, var(--Primary-Pri-1, #003292) 0%, #00E7DD 100%)',
        'gradient5-p3':
          'linear-gradient(90deg, var(--Primary-Pri-1, color(display-p3 0 0.1882 0.5294)) 0%, color(display-p3 0.3647 0.8902 0.8627) 100%)',
        gradientOrange: 'linear-gradient(180deg, #EB642B 37%, #FFBB3D 100%)',
        progress:
          'linear-gradient(190deg, #EB642B -0.39%, #F48B34 49.81%, #FDB33C 100%)',
      },
      borderRadius: {
        10: '10px',
      },
      boxShadow: {
        custom: '0px 4px 10px 0px rgba(0, 0, 0, 0.06)',
      },
    },
  },
  plugins: [
    forms({
      strategy: 'base',
    }),
    tailwindScrollbar,
    layouts,
    sidebar,
    buttons,
    forms,
    tables,
    flatpicker,
    apexchart,
  ],
};
