// ===== 공통: 길이 제한 =====
export const NAME_MAX_LEN = 30;
export const SURNAME_MAX_LEN = 30;
export const EMAIL_MAX_LEN = 64;
export const PASSWORD_LENGTH = { min: 8, max: 12 } as const;

// ===== 성 =====
// 영문, 한글 + 하이픈('-') 허용, 하이픈은 최대 2개, 전체 길이 30자
export const SURNAME_REGEX =
  /^(?=.{1,30}$)[A-Za-z가-힣]+(?:-[A-Za-z가-힣]+){0,2}$/;

// ===== 이름 =====
// 영문, 한글만, 최대 30자
export const NAME_REGEX = /^[A-Za-z가-힣]{1,30}$/;

// ===== 이메일 =====
// 문자구성: 영문/숫자/특수문자 3개(- _ .)만 허용, 최대 64자
export const EMAIL_ALLOWED_CHARS_REGEX = /^[A-Za-z0-9._@-]+$/;
export const EMAIL_MAXLEN_REGEX = new RegExp(`^.{1,${EMAIL_MAX_LEN}}$`);
export const EMAIL_REGEX = /^[A-Za-z0-9._-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;

// ===== 비밀번호 =====
// 특수문자: ! @ # $ % ^ * ( ) _ - + = ~  → 총 14개
export const PASSWORD_SPECIALS = `!@#$%^*()_\\-+=~`;
export const PASSWORD_ALLOWED_REGEX = new RegExp(
  `^[A-Za-z0-9${PASSWORD_SPECIALS}]{${PASSWORD_LENGTH.min},${PASSWORD_LENGTH.max}}$`,
);

// 조합 규칙
export const UPPERCASE_REGEX = /[A-Z]/; // 대문자
export const LOWERCASE_REGEX = /[a-z]/; // 소문자
export const DIGIT_REGEX = /\d/; // 숫자
export const SPECIAL_REGEX = /[!@#$%^*()_\-+=~]/; // 특수문자(정의된 14개)

export const PASSWORD_RULES = [
  { name: 'uppercase', regex: UPPERCASE_REGEX }, // 대문자 1개 이상
  { name: 'lowercase', regex: LOWERCASE_REGEX }, // 소문자 1개 이상
  { name: 'digit', regex: DIGIT_REGEX }, // 숫자 1개 이상
  { name: 'special', regex: SPECIAL_REGEX }, // 특수문자 1개 이상
];

// ===== 유틸 =====
export function validateFirstName(value: string) {
  return value.length <= NAME_MAX_LEN && NAME_REGEX.test(value);
}

export function validateLastName(value: string) {
  return SURNAME_REGEX.test(value);
}

export function validateEmail(value: string) {
  return (
    EMAIL_MAXLEN_REGEX.test(value) &&
    EMAIL_ALLOWED_CHARS_REGEX.test(value) &&
    EMAIL_REGEX.test(value)
  );
}

const isAlphaNum = (ch: number) =>
  (ch >= 48 && ch <= 57) || (ch >= 65 && ch <= 90) || (ch >= 97 && ch <= 122);

const isSeq = (a: number, b: number, c: number) =>
  (b === a + 1 && c === b + 1) || (b === a - 1 && c === b - 1);

// 3자리 동일/연속(증가/감소) 금지 검사
function calcTripleSeqOk(value: string) {
  if (!value) return false; // 비어있으면 아직 충족 X 취급
  for (let i = 0; i < value.length - 2; i++) {
    const a = value.charCodeAt(i);
    const b = value.charCodeAt(i + 1);
    const c = value.charCodeAt(i + 2);

    // 동일 3연속
    if (value[i] === value[i + 1] && value[i + 1] === value[i + 2])
      return false;

    // 영문/숫자 연속 3자리
    if (isAlphaNum(a) && isAlphaNum(b) && isAlphaNum(c) && isSeq(a, b, c)) {
      return false;
    }
  }
  return true;
}

export function validatePassword(value: string) {
  const lengthOk =
    value.length >= PASSWORD_LENGTH.min && value.length <= PASSWORD_LENGTH.max;

  const allowedOk = PASSWORD_ALLOWED_REGEX.test(value);

  const rules = {
    uppercase: UPPERCASE_REGEX.test(value),
    lowercase: LOWERCASE_REGEX.test(value),
    digit: DIGIT_REGEX.test(value),
    special: SPECIAL_REGEX.test(value),
  };

  const categoriesCount =
    (rules.uppercase ? 1 : 0) +
    (rules.lowercase ? 1 : 0) +
    (rules.digit ? 1 : 0) +
    (rules.special ? 1 : 0);

  const atLeast3Ok = categoriesCount >= 3;

  const tripleSeqOk = calcTripleSeqOk(value);

  // “최종 OK”를 util에서 바로 쓰고 싶을 때 편하도록 포함
  const allRulesOk = lengthOk && allowedOk && atLeast3Ok && tripleSeqOk;

  return {
    lengthOk,
    allowedOk,
    rules,
    categoriesCount,
    atLeast3Ok,
    tripleSeqOk,
    allRulesOk,
  };
}

export function validatePasswordConfirm(password: string, confirm: string) {
  return password === confirm;
}
