import Layout from './Layout';
import { Button } from '@/Common/Components/common/Button';
import { AlertPopupProps } from '@/types';

const TwoButtonPopup = ({
  isOpen,
  title,
  text,
  buttonText,
  secondButtonText,
  onClose,
  onConfirm,
}: AlertPopupProps) => {
  return (
    <Layout isOpen={isOpen}>
      <div className="w-[600px] bg-white rounded-10">
        <div className="w-full py-6 px-[30px] border-b border-gray-4">
          {title && <div className="subtitle3">{title}</div>}
        </div>

        <div className="p-[30px]">
          <div className="mb-10 body1">
            {text?.split('\n').map((line, index) => (
              <span key={index}>
                {line}
                <br />
              </span>
            ))}
          </div>
          <div className="f-c-e gap-2">
            <Button
              variant={'bt_secondary'}
              label={buttonText}
              onClick={onConfirm}
            />
            <Button
              variant={'bt_primary'}
              label={secondButtonText}
              onClick={onClose}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default TwoButtonPopup;
