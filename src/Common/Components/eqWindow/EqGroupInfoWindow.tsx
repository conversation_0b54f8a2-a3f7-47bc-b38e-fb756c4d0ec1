import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { GeneralInfoWindow } from '@/logiMaps/react/general/InfoWindow';
import { Cross1Icon } from '@radix-ui/react-icons';
import eqstat_idle_e from '@/assets/images/badge/idle_e.svg';
import eqstat_operation_e from '@/assets/images/badge/operation_e.svg';
import eqstat_fault_e from '@/assets/images/badge/fault_e.svg';
import eqstat_maint_e from '@/assets/images/badge/maint_e.svg';

export interface EqGroupInfoWindowProps {
  id: string;
  position: { lat: number; lng: number };
  items: {
    id: string;
    equipmentId: string;
    modelName: string;
    vehicleNum: string;
    mileage: string;
    status: string;
  }[];
  isClosed?: boolean;
  onClose?: () => void;
}

const EqGroupInfoWindow = (props: EqGroupInfoWindowProps) => {
  const { t } = useTranslation();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'IDLE':
        return eqstat_idle_e;
      case 'OPERATION':
        return eqstat_operation_e;
      case 'FAULT':
        return eqstat_fault_e;
      case 'MAINT':
        return eqstat_maint_e;
      default:
        return undefined;
    }
  };

  return (
    <GeneralInfoWindow
      id={props.id}
      position={{ lat: props.position.lat, lng: props.position.lng }}
      pixelOffset={[0, -20]}
      zIndex={10}
    >
      <div className="w-[510px] max-h-[290px] px-1 overflow-y-scroll ">
        <div className="f-c-e">
          <Cross1Icon
            onClick={props.onClose}
            width={20}
            height={20}
            className="mb-[10px] cursor-pointer"
          />
        </div>
        <table>
          <thead>
            <tr className="text-left [&_th]:subtitle6">
              <th>{t('ModelName')}</th>
              <th>{t('VehicleNumber')}</th>
              <th>{t('Mileage')}</th>
              <th>{t('Status')}</th>
            </tr>
          </thead>
          <tbody>
            {props.items.map((row, index) => (
              <tr key={index} className="[&_td]:body5">
                <td>
                  <Link
                    to={'/eq_list'}
                    state={{ equipmentId: row.equipmentId }}
                    className="underline"
                  >
                    {row.modelName}
                  </Link>
                </td>
                <td>{row.vehicleNum}</td>
                <td>{row.mileage}</td>
                <td>
                  {getStatusIcon(row.status) && (
                    <img src={getStatusIcon(row.status)} alt={row.status} />
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </GeneralInfoWindow>
  );
};

export default EqGroupInfoWindow;
