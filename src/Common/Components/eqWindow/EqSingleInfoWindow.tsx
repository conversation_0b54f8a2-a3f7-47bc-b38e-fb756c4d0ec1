import { useEffect, useState } from 'react';
import { GeneralInfoWindow } from '@/logiMaps/react/general/InfoWindow';
import { getEqStatList } from '@/Common/function/functions.ts';
import { EquipmentType } from '@/types/EquipmentType';

export interface EqSingleInfoWindowProps {
  position: { lat: number; lng: number };
  item: EquipmentType.FilteredMapItem;
  onClose?: () => void;
}

const EqSingleInfoWindow = (props: EqSingleInfoWindowProps) => {
  const [, setEqStats] = useState<{ key: string; value: string }[]>([]);

  /** useEffect */
  useEffect(() => {
    const eqStatList = getEqStatList(
      props.item.operationStatus,
      props.item.breakdownStatus,
    );
    setEqStats(
      eqStatList.map((item) => ({
        key: item,
        value: item,
      })),
    );
  }, [props.item]);

  return (
    <GeneralInfoWindow
      id={props.item.id}
      position={{ lat: props.position.lat, lng: props.position.lng }}
      zIndex={10}
      pixelOffset={[0, -10]}
    >
      <div
        className="
          [&>span]:px-2
          [&>span]:caption2
          [&>span]:text-white
          [&>span]:border-r
          [&>span:last-child]:border-0
          [&>span]:border-white
        "
      >
        <span>{props.item.modelName}</span>
        <span>{props.item.equipmentId}</span>
        <span>{props.item.mileage}</span>
      </div>
    </GeneralInfoWindow>
  );
};

export default EqSingleInfoWindow;
