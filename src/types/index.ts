// 맵 엔진
export const MapEngine = {
  source: () => {
    return 'logi';
  },
};

// 테스트 더미
export const DemoTest = {
  isRandomOn: (flag?: boolean) => {
    if (flag !== undefined) {
      return flag;
    }
    // 환경 변수에서 DEMO_MODE 값을 읽어옴
    const envDemoMode = import.meta.env.VITE_DEMO_MODE;
    if (envDemoMode !== undefined) {
      return envDemoMode === 'true';
    }
    return false;
  },
};

// 키, 값 타입
export interface KeyStringObjectType<P = unknown> {
  [key: string]: P;
}

export type EnumItemType = {
  key: string;
  value: string;
};

// 팝업 기본 타입
export interface PopupProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

// 알럿 타입 팝업 타입
export interface AlertPopupProps extends PopupProps {
  title?: string;
  text?: string;
  indexOf?: number;
  buttonText?: string;
  secondButtonText?: string;
  children?: React.ReactNode;
  data?: any;
  calendarId?: string;
}

// 알람 탭 테이블
export interface FaultsTableColumnProps {
  date: string;
  mileage: string;
  severity: string;
  status: string;
  symptoms: string;
}

//
export interface ExpendableTabProps {
  itemNo: string;
  item: string;
  term: string; //interval
  totalCnt: string; //maintenanceTotalCount
  mileage: number;
  lastDate: string;
  remainHour: string;
  alarm: string;
  alarmSend: boolean;
  mantAlarm: string;
}

// 드롭 다운 타입
export interface DropdownOption {
  key: string;
  value: string;
  icon?: JSX.Element;
  subLabel?: string;
  status?: string;
  statusLabel?: string;
}

// 화면 사이즈 훅
export interface Dimension {
  width: number;
  height: number;
}

// 장비 상태
export type EqOperationStatus = {
  running: boolean;
  idle: boolean;
};

// 고장 상태
export type EqBreakdownStatus = {
  breakdown: boolean;
  repairing: boolean;
  none: boolean;
};

export enum DriverStatusType {
  OnDuty = 'OnDuty',
  Idle = 'Idle',
}

export function toDriverStatusType(
  value: string | null | undefined,
): DriverStatusType {
  if (value?.toLowerCase() === 'on_duty') {
    return DriverStatusType.OnDuty;
  }
  if (value?.toLowerCase() === 'idle') {
    return DriverStatusType.Idle;
  }
  return DriverStatusType.Idle; // 기본값
}

export enum DispatchStatusType {
  Completed = 'Completed',
  Pending = 'Pending',
  InProgress = 'InProgress',
}

export function toDispatchStatusType(
  value: string | null | undefined,
): DispatchStatusType {
  if (value?.toLowerCase() === 'completed') {
    return DispatchStatusType.Completed;
  }
  if (value?.toLowerCase() === 'pending') {
    return DispatchStatusType.Pending;
  }
  if (value?.toLowerCase() === 'inprogress') {
    return DispatchStatusType.InProgress;
  }
  return DispatchStatusType.Pending; // 기본값
}

export enum OperationStatusType {
  InOperation = 'InOperation',
  Idle = 'Idle',
}

export function toOperationStatusType(
  value: string | null | undefined,
): OperationStatusType {
  if (
    value?.toLowerCase() === 'running' ||
    value?.toLowerCase() === 'inoperation'
  ) {
    return OperationStatusType.InOperation;
  }
  if (value?.toLowerCase() === 'idle') {
    return OperationStatusType.Idle;
  }
  return OperationStatusType.Idle; // 기본값
}

export enum GpsStatusType {
  Connected = 'Connected',
  Disconnected = 'Disconnected',
}

export function toGpsStatusType(
  value: string | null | undefined,
): GpsStatusType {
  if (value?.toLowerCase() === 'connected') {
    return GpsStatusType.Connected;
  }
  if (value?.toLowerCase() === 'disconnected') {
    return GpsStatusType.Disconnected;
  }
  return GpsStatusType.Disconnected; // 기본값
}

export enum BreakdownStatusType {
  Breakdown = 'Breakdown',
  Repairing = 'Repairing',
  None = 'None',
}

export function toBreakdownStatusType(
  value: string | null | undefined,
): BreakdownStatusType {
  if (value?.toLowerCase() === 'breakdown') {
    return BreakdownStatusType.Breakdown;
  }
  if (value?.toLowerCase() === 'repairing') {
    return BreakdownStatusType.Repairing;
  }
  return BreakdownStatusType.None; // 기본값
}

export function toStatusLabel(
  value: OperationStatusType | BreakdownStatusType | null | undefined,
): string {
  if (value === OperationStatusType.InOperation) {
    return 'In Operation';
  }
  if (value === OperationStatusType.Idle) {
    return 'Idle';
  }
  if (value === BreakdownStatusType.Breakdown) {
    return 'Fault';
  }
  if (value === BreakdownStatusType.Repairing) {
    return 'In Maintenance';
  }
  if (value === BreakdownStatusType.None) {
    return 'None';
  }
  if (value === null || value === undefined) {
    return 'Unknown';
  }
  return 'Idle'; // 기본값
}

export enum StatusType {
  InOperation = 'InOperation',
  Idle = 'Idle',
  InMaintenance = 'InMaintenance',
  Fault = 'Fault',
}

export function toStatusType(value: string | null | undefined): StatusType {
  if (value?.toLowerCase() === 'inoperation') {
    return StatusType.InOperation;
  }
  if (value?.toLowerCase() === 'idle') {
    return StatusType.Idle;
  }
  if (value?.toLowerCase() === 'inmaintenance') {
    return StatusType.InMaintenance;
  }
  if (value?.toLowerCase() === 'fault') {
    return StatusType.Fault;
  }
  return StatusType.Idle; // 기본값
}

// 모달 타입
// export interface ModalProps {
//   isOpen: boolean;
//   toggleModal: unknown;
//   title: string;
//   divClass: string;
//   content: string;
//   onDiscard: unknown;
//   sizeClass: string;
//   spaceClass: string;
// }

// 레이 아웃 타입
// export interface LayoutProps {
//   i: string;
//   x: number;
//   y: number;
//   w: number;
//   h: number;
//   static: boolean;
//   minW?: number;
//   maxW?: number;
// }

// export interface CustomCalendarAddData {
//   calendarType?: string;
//   publicStatus?: string;
//   countryList?: string[];
//   colorType?: string;
//   title?: string;
//   context?: string;
//   repeatType?: string;
//   startDate?: string;
//   startTime?: string;
//   endDate?: string;
//   endTime?: string;
// }

// 아코디언 ?? 사용처 불명?
// export interface AccordionProps {
//   items?: AccordionItem[];
//   heading?: string;
//   initialOpenIndex?: number;
//   borderClass?: string;
//   buttonClass?: string;
//   activeClass?: string;
//   isSpace?: boolean;
// }

// 아코디언 ?? 사용처 불명?
// export interface AccordionItem {
//   icon?: React.ReactNode; // 아이콘 요소 (ReactNode 타입)
//   title: string; // 제목
//   arrow?: React.ReactNode; // 화살표 아이콘 요소 (ReactNode 타입)
// }

// AI chat bot 말풍선 타입
// export interface AnswerAndQuestionProps {
//   // 응답 메시지 , 질문 메시지
//   message?: string;
// }

// Q&A 디테일 뷰 컴포넌트 타입
// export interface ViewDetailsProps {
//   onAnswerClick: () => void;
// }

// export interface QaData {
//   no?: string;
//   inquiry?: string;
//   title?: string;
//   writer?: string;
//   date?: string;
//   state?: string;
//   fileName?: string[];
// }

// export interface TsgData {
//   check?: boolean;
//   modelGroup?: string;
//   type?: string;
//   spn?: string;
//   fmi?: string;
//   tsg?: string;
//   read?: string;
//   remark?: string;
// }

//top menu 색깔 변경을 위한 작업
// export interface TopMenuProps {
//   isScrolled?: boolean;
// }
