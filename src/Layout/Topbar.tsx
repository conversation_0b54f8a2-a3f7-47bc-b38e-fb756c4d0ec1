import { useTranslation } from 'react-i18next';
import React from 'react';
import { Link } from 'react-router-dom';
import dayjs from 'dayjs';
import Type from '@/Common/topbar/Type.tsx';
import AiChatbot from '@/assets/images/svg/etc/AiChatbot.tsx';
import { LanguageButton } from '@/Common/topbar/Language.tsx';
import NotificationButton from '@/Common/topbar/NotificationButton.tsx';
import InfoButton from '@/Common/topbar/InfoButton.tsx';

const Topbar = () => {
  const { t } = useTranslation();

  const today = dayjs().format('MMMM D [at] h:mm A');

  return (
    <React.Fragment>
      <div
        className={`max-w-[calc(100%-240px)] min-w-0 w-full mx-auto py-5 px-8 f-c-b gap-4 bg-white fixed z-50`}
      >
        <div className="f-c gap-5">
          <Type />
          {/* <div className="py-1 px-[14px] bg-semantic-1 rounded-full body3 text-white">
            {t('TrialEnds')} {today}
          </div> */}
        </div>
        <div className="f-c gap-4">
          <Link
            to="https://assistant.cartamobility.com/"
            target="_blank"
            className="mr-3"
          >
            <AiChatbot />
          </Link>
          <LanguageButton />
          <NotificationButton />
          <InfoButton />
        </div>
      </div>
    </React.Fragment>
  );
};

export default Topbar;
