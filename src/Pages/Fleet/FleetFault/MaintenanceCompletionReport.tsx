import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import UseFleetPopup from '@/Pages/Fleet/Component/UseFleetPopup';
import { CustomFrame } from '@/Pages/CustomFrame';
import { Button } from '@/Common/Components/common/Button';
import LineCon from '@/Common/Components/layout/LineCon';
import LineLabel from '@/Common/Components/layout/LineLabel';
import Input from '@/Common/Components/common/Input';
import DropDown from '@/Common/Components/common/DropDown';
import FileDropDown from '@/Common/Components/common/FileDropDown';

const MaintenanceCompletionReport = () => {
  const { t } = useTranslation();

  const navigate = useNavigate();

  const { openFFMaintenanceOutPopup } = UseFleetPopup();

  const isPageOut = () => {
    openFFMaintenanceOutPopup(() => {
      navigate(-1);
    });
  };

  const codeOption = [
    { key: '$', value: '$' },
    { key: 'C$', value: 'C$' },
    { key: 'Mex$', value: 'Mex$' },
    { key: '₩', value: '₩' },
  ];

  // 글자 수 제한
  const [details, setDetails] = useState('');
  const MAX_DETAILS_LEN = 500;

  return (
    <CustomFrame
      name={t('MaintenanceCompletionReport')}
      back
      onBackClick={isPageOut}
    >
      <section className="wrap-layout [&_h2]:mb-[23px] [&_h2]:app-button [&_span]:w-[110px]">
        {/* Repair Details */}
        <article>
          <h2 className="f-c-b">
            {t('Repair Details')}
            <Button variant={'bt_primary'} label={'Register'} />
          </h2>
          <div className="space-y-3">
            <LineCon className="gap-5">
              <LineLabel
                label={t('Mileage')}
                required={true}
                className="subtitle4"
              />
              <Input placeholder={t('Mileage')} widthSize="lg" />
            </LineCon>
            <LineCon className="gap-5">
              <LineLabel label={t('MaintenanceCode')} className="subtitle4" />
              <div className="f-c gap-[10px]">
                <Input placeholder={t('maintenanceCode')} widthSize="lg" />
                <DropDown options={codeOption} placeholder="$" />
              </div>
            </LineCon>
          </div>
        </article>

        <div className="divider my-10" />

        {/* Service Center Information */}
        <article>
          <h2>{t('ServiceCenterInformation')}</h2>
          <div className="space-y-3">
            <LineCon className="gap-5">
              <LineLabel label={t('Name')} className="subtitle4" />
              <Input placeholder={t('Name')} widthSize="lg" />
            </LineCon>
            <LineCon className="gap-5">
              <LineLabel label={t('Location')} className="subtitle4" />
              <Input placeholder={t('Location')} className="w-[1000px]" />
            </LineCon>
            <LineCon className="gap-5">
              <LineLabel label={t('LatitudeLongitude')} className="subtitle4" />
              <Input placeholder={t('LatitudeLongitude')} widthSize="lg" />
            </LineCon>
          </div>
        </article>

        <div className="divider my-10" />

        {/* etc */}
        <article className="space-y-3">
          <LineCon style={{ alignItems: 'start' }} className="gap-5">
            <LineLabel label={t('MaintenanceDetails')} className="subtitle4" />
            <div className="w-full">
              <textarea
                name="details"
                id="details"
                placeholder={t('MaintenanceDetails')}
                value={details}
                onChange={(e) => setDetails(e.currentTarget.value)}
                maxLength={MAX_DETAILS_LEN}
                className="w-full h-[180px] min-h-[120px] max-h-[240px] p-4 pr-14 border border-gray-6 rounded-md placeholder:body2 placeholder:text-gray-7 focus:outline-none focus:ring-0 focus:border-gray-6"
                aria-describedby="details-counter"
              />
              <p
                id="details-counter"
                className="mt-[-4px] f-je caption4 text-gray-12"
              >
                {details.length}/{MAX_DETAILS_LEN}
              </p>
            </div>
          </LineCon>
          <LineCon className="gap-5">
            <LineLabel label={t('AttatchFile')} className="subtitle4" />
            <FileDropDown />
          </LineCon>
        </article>
      </section>
    </CustomFrame>
  );
};

export default MaintenanceCompletionReport;
