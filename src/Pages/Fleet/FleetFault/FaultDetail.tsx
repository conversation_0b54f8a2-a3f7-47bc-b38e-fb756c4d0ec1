import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import UseFleetPopup from '../Component/UseFleetPopup';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { Button } from '@/Common/Components/common/Button';
import LineCon from '@/Common/Components/layout/LineCon';
import LineLabel from '@/Common/Components/layout/LineLabel';
import SummaryData from '@/Common/Components/etc/SummaryData';

type FaultDetail = {
  issueDescription?: string;
  issuePhotoUrls?: string[];
  repairDescription?: string;
  repairPhotoUrls?: string[];
};

const FaultDetail = (props: { faultDetail?: FaultDetail }) => {
  const { t } = useTranslation();

  const [previewSrc, setPreviewSrc] = useState<string | null>(null);
  const openPreview = (src: string) => setPreviewSrc(src);
  const closePreview = () => setPreviewSrc(null);

  const { openFFFaultChangeStatusPopup, openFFLocationPopup } = UseFleetPopup();

  const { faultDetail } = props;

  // Date & Time
  const dataData = [
    { label: 'FaultOccurrence', value: '2024-12-12 12:12' },
    { label: 'MMaintenanceCompletionodel', value: '2024-12-14 10:12' },
  ];

  // Vehicle & Driver Information
  const vehicleDriverData = [
    { label: 'ModelName', value: '25B-X' },
    { label: 'VehicleNumber', value: 'ABC-1234' },
    { label: 'DriverName', value: 'Olivia Grace Taylor' },
  ];

  // Mileage
  const mileageData = [
    { label: 'AtBreakdown', value: '25,312mi' },
    { label: 'AtCompletion', value: '27,312mi' },
  ];

  // Fault Information
  const faultData = [
    { label: 'Severity', value: 'High' },
    { label: 'Status', value: 'In Maintenance' },
    {
      label: 'Location',
      value: (
        <span className="blue-underline" onClick={openFFLocationPopup}>
          1600 Wilshire Blvd #205, Los Angeles CA 90017, USA
        </span>
      ),
    },
  ];

  // Service Center Information
  const serviceData = [
    { label: 'CompanyName', value: 'RapidFix Auto Repair' },
    { label: 'Location', value: '1457 W 190th St, Gardena, CA 90248, USA' },
    { label: 'LatitudeLongitude', value: '33.857430 / -118.301760' },
  ];

  // Maintenance Cost
  const maintenanceData = [{ label: '', value: '$949.66' }];

  // 이슈 내용
  const issueDescription = faultDetail?.issueDescription ?? '';
  const issuePhotos = (faultDetail?.issuePhotoUrls ?? []).slice(0, 5);

  // 수리 내용
  const repairDescription = faultDetail?.repairDescription ?? '';
  const repairPhotos = (faultDetail?.repairPhotoUrls ?? []).slice(0, 5);

  return (
    <CustomFrame name={t('FaultDetails')} back={false}>
      <section className="wrap-layout relative">
        <Button
          variant="bt_primary"
          label="Register"
          onClick={openFFFaultChangeStatusPopup}
          className="absolute right-0 top-[-60px]"
        />

        {/* 요약 데이터 */}
        <article className="space-y-3 [&>LineCon]:f-c [&>div]:gap-[30px] [&_span]:w-[220px]">
          <LineCon>
            <LineLabel label={t('DateTime2')} className="subtitle4" />
            <SummaryData details={dataData} />
          </LineCon>
          <LineCon>
            <LineLabel
              label={t('VehicleDriverInformation')}
              className="subtitle4"
            />
            <SummaryData details={vehicleDriverData} />
          </LineCon>
          <LineCon>
            <LineLabel label={t('Mileage')} className="subtitle4" />
            <SummaryData details={mileageData} />
          </LineCon>
          <LineCon>
            <LineLabel label={t('FaultInformation')} className="subtitle4" />
            <SummaryData details={faultData} />
          </LineCon>
          <LineCon>
            <LineLabel
              label={t('ServiceCenterInformation')}
              className="subtitle4"
            />
            <SummaryData details={serviceData} />
          </LineCon>
          <LineCon>
            <LineLabel label={t('MaintenanceCost')} className="subtitle4" />
            <SummaryData details={maintenanceData} />
          </LineCon>
        </article>

        <div className="divider my-[30px]" />

        {/* 이슈 내용 */}
        <article>
          <div className="mb-[60px]">
            <h3 className="mb-3 subtitle3">{t('IssueDescription')}</h3>
            <p className="body2">{issueDescription}</p>
          </div>
          <div>
            <h3 className="mb-3 subtitle4">{t('FaultPhotos')}</h3>
            <div className="f-c gap-3">
              {issuePhotos.map((src, idx) => (
                <img
                  key={idx}
                  src={src}
                  alt={`fault-photo-${idx + 1}`}
                  className="w-[300px] h-[330px] rounded-xl object-cover cursor-pointer"
                  loading="lazy"
                  onClick={() => openPreview(src)}
                />
              ))}
            </div>
          </div>
        </article>

        {/* 경계선 */}
        <div className="divider my-[30px]" />

        {/* 수리 내용 */}
        <article>
          <div className="mb-[60px]">
            <h3 className="mb-3 subtitle3">{t('RepairDetails')}</h3>
            <p className="body2">{repairDescription}</p>
          </div>
          <div>
            <h3 className="mb-3 subtitle4">{t('CompletionPhotos')}</h3>
            <div className="f-c gap-3">
              {repairPhotos.map((src, idx) => (
                <img
                  key={idx}
                  src={src}
                  alt={`fault-photo-${idx + 1}`}
                  className="w-[300px] h-[330px] rounded-xl object-cover"
                  loading="lazy"
                  onClick={() => openPreview(src)}
                />
              ))}
            </div>
          </div>
        </article>

        {/* 이미지 확대 */}
        {previewSrc && (
          <div
            className="f-c-c fixed inset-0 z-[1000] bg-black/80"
            onClick={closePreview}
          >
            <TransformWrapper
              minScale={1}
              initialScale={1}
              doubleClick={{ mode: 'zoomIn' }}
              wheel={{ step: 0.2 }}
              pinch={{ disabled: false }}
              panning={{ disabled: false }}
            >
              <div onClick={(e) => e.stopPropagation()}>
                <TransformComponent>
                  <img
                    src={previewSrc}
                    alt="preview"
                    className="max-w-[90vw] max-h-[85vh] rounded-xl object-contain"
                    draggable={false}
                  />
                </TransformComponent>
              </div>
            </TransformWrapper>
          </div>
        )}
      </section>
    </CustomFrame>
  );
};

export default FaultDetail;
