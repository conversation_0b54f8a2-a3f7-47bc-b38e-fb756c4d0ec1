import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { ColumnDef, Row } from '@tanstack/react-table';
import { useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import dayjs from 'dayjs';
import { DispatchStatusType, toDispatchStatusType } from '@/types';
import useDispatchPopup from './components/popup/useDIspatchPopup';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import Input from '@/Common/Components/common/Input.tsx';
import { Button } from '@/Common/Components/common/Button.tsx';
import CommonTable from '@/Common/Components/common/CommonTable';
import { dispatchApi } from '@/api';
import { AdminItineraryDispatchApiGetAdminItineraryDispatchPageRequest } from '@/api/generated';

type DispatchHistoryPage = {
  rows: DispatchHistoryRow[];
  page: {
    pageSize: number;
    totalCnt: number;
    pageNum: number;
  };
};

type DispatchHistoryRow = {
  itineraryDispatchId: number;
  title: string;
  date: string;
  status: DispatchStatusType;
  driver: string;
  vehicle: string;
  total: number;
  deliveries: number;
};

const DispatchHistory = () => {
  const { t } = useTranslation();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      plateNo: '',
      driverName: '',
    },
  });

  // 입력 폼 값
  const [formValues, setFormValues] = useState({
    startDate: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
    endDate: dayjs().format('YYYY-MM-DD'),
    plateNo: '',
    driverName: '',
    page: 0,
    size: 5,
    sort: 'driverName,asc',
  });

  const { openDispatchDetails } = useDispatchPopup();

  const columns: ColumnDef<DispatchHistoryRow>[] = [
    {
      header: t('Title'),
      accessorKey: 'title',
    },
    {
      header: t('Date'),
      accessorKey: 'date',
    },
    {
      header: t('Status'),
      accessorKey: 'status',
      cell: ({ row }: { row: Row<DispatchHistoryRow> }) => {
        const value = row.original.status;
        let color = '';
        switch (value) {
          case DispatchStatusType.Completed:
            color = 'text-semantic-1'; // 초록
            break;
          case DispatchStatusType.Pending:
            color = 'text-semantic-4'; // 빨강
            break;
          case DispatchStatusType.InProgress:
            color = 'text-semantic-2'; // 파랑
            break;
          default:
            color = 'text-gray-10';
        }
        return <span className={`${color} body2`}>{value}</span>;
      },
    },
    {
      header: t('DriverName'),
      accessorKey: 'driver',
    },
    {
      header: t('VehicleNumber'),
      accessorKey: 'vehicle',
    },
    {
      header: t('TotalStops'),
      accessorKey: 'total',
    },
    {
      header: t('CompletedDeliveries'),
      accessorKey: 'deliveries',
    },
  ];

  /** Params */
  const [dispatchHistoryParams, setDispatchHistoryParams] = useState<
    AdminItineraryDispatchApiGetAdminItineraryDispatchPageRequest | undefined
  >({
    startScheduledDt: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
    endScheduledDt: dayjs().format('YYYY-MM-DD'),
    plateNo: '',
    driverName: '',
    page: 0,
    size: 10,
  });

  /** useQuery */
  const { data: dispatchHistoryPage } = useQuery<DispatchHistoryPage | null>({
    queryKey: ['/api/admin/itinerary-dispatch/page', dispatchHistoryParams],
    queryFn: async () => {
      try {
        const result = await dispatchApi.getAdminItineraryDispatchPage(
          dispatchHistoryParams,
        );
        return {
          rows:
            result?.data?.content?.map((row) => ({
              itineraryDispatchId: row.itineraryDispatchId ?? 0,
              title: row.itineraryPlanName ?? '',
              date: row.scheduledDt ?? '',
              status: toDispatchStatusType(row.itineraryStatus),
              driver: row.driver?.driverName ?? '',
              vehicle: row.equipment?.plateNo ?? '',
              total: row.totalItineraryRouteCount ?? 0,
              deliveries: row.completedItineraryRouteCount ?? 0,
            })) ?? [],
          page: {
            pageSize: result?.data?.page?.size ?? 10,
            totalCnt: result?.data?.page?.totalElements ?? 0,
            pageNum: result?.data?.page?.number ?? 0,
          },
        };
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    enabled: !!dispatchHistoryParams,
  });

  const handleSearch = () => {
    setDispatchHistoryParams((prev) => ({
      ...prev,
      startScheduledDt: dayjs(formValues.startDate).format('YYYY-MM-DD'),
      endScheduledDt: dayjs(formValues.endDate).format('YYYY-MM-DD'),
      plateNo: formValues.plateNo,
      driverName: formValues.driverName,
      page: 0, // 검색 시 첫 페이지로 리셋
    }));
  };

  return (
    <CustomFrame name={t('DispatchHistory')} back={false}>
      <section className="wrap-layout">
        <form onSubmit={handleSubmit(handleSearch)}>
          <div className="mb-14 f-c gap-4">
            <div className="f-c gap-[10px]">
              <FromToSelector
                initValue={{
                  start: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
                  end: dayjs().format('YYYY-MM-DD'),
                }}
                onInit={(startDate, endDate) => {
                  setFormValues((prev) => ({
                    ...prev,
                    startDate: dayjs(startDate).format('YYYY-MM-DD'),
                    endDate: dayjs(endDate).format('YYYY-MM-DD'),
                  }));
                }}
                onChange={(startDate, endDate) =>
                  setFormValues((prev) => ({
                    ...prev,
                    startDate: dayjs(startDate).format('YYYY-MM-DD'),
                    endDate: dayjs(endDate).format('YYYY-MM-DD'),
                  }))
                }
              />
              {/* 차량 번호 */}
              <Input
                placeholder={t('VehicleNumber')}
                value={formValues.plateNo}
                {...register('plateNo', {
                  maxLength: {
                    value: 20,
                    message: 'Maximum 20 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9\-_ ]*$/,
                    message: 'Only A-Z, 0-9, -, _ allowed.',
                  },
                })}
                error={errors.plateNo?.message}
                onChange={(e) =>
                  setFormValues((prev) => ({
                    ...prev,
                    plateNo: e.target.value,
                  }))
                }
                reset={() =>
                  setFormValues((prev) => ({
                    ...prev,
                    plateNo: '',
                  }))
                }
              />
              {/* 기사명 */}
              <Input
                placeholder={t('DriverName')}
                value={formValues.driverName}
                {...register('driverName', {
                  maxLength: {
                    value: 20,
                    message: 'Maximum 20 characters allowed.',
                  },
                })}
                error={errors.driverName?.message}
                onChange={(e) =>
                  setFormValues((prev) => ({
                    ...prev,
                    driverName: e.target.value,
                  }))
                }
                reset={() =>
                  setFormValues((prev) => ({
                    ...prev,
                    driverName: '',
                  }))
                }
              />
            </div>
            <Button type="submit" variant="bt_primary" label={t('Search')} />
          </div>
        </form>

        <CommonTable
          columns={columns}
          data={dispatchHistoryPage?.rows || []}
          isPagination={true}
          isCheckbox={false}
          onRowClick={async (row: DispatchHistoryRow) => {
            try {
              const detailResult = await dispatchApi.getAdminItineraryDispatch({
                itineraryDispatchId: row.itineraryDispatchId,
              });
              openDispatchDetails(() => {}, detailResult.data);
            } catch (error) {
              console.error('배차 상세 정보 조회 에러:', error);
            }
          }}
          tbodyclassName="cursor-pointer"
          customPageSize={dispatchHistoryPage?.page.pageSize ?? 0}
          totalCount={dispatchHistoryPage?.page.totalCnt ?? 0}
          currentPage={
            dispatchHistoryPage?.page.pageNum
              ? dispatchHistoryPage.page.pageNum + 1
              : 1
          }
          onPageChange={(page: number) => {
            setDispatchHistoryParams((prevState) =>
              prevState ? { ...prevState, page: page - 1 } : undefined,
            );
          }}
        />
      </section>
    </CustomFrame>
  );
};

export default DispatchHistory;
