import { useTranslation } from 'react-i18next';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Cross1Icon } from '@radix-ui/react-icons';
import { AlertPopupProps, MapEngine } from '@/types';
import { GeneralPolyline } from '@/logiMaps/react/general/Poly';
import { GeneralMap, GeneralMapAdapter } from '@/logiMaps/react/general/Map';
import TrackingMarker from '@/Common/Components/Marker/TrackingMarker';
import EqSingleMarker from '@/Common/Components/Marker/EqSingleMarker';
import EqPositionInfoWindow from '@/Common/Components/eqWindow/EqPositionInfoWindow';
import ZoomController from '@/Common/Components/map/ZoomController';
import Layout from '@/Common/Popup/Layout';
import { Button } from '@/Common/Components/common/Button';
import SummaryData from '@/Common/Components/etc/SummaryData';
import TimelineBadge from '@/assets/images/svg/30/TimelineBadge';
import { dispatchApi } from '@/api';
import { AdminItineraryDispatchApiGetAdminItineraryDispatchRequest } from '@/api/generated';

/** Types */
interface Segment {
  tagName: string;
  address: string;
  arrivalTime: string;
  estimatedTime: string;
  completed: boolean;
  path: { lat: number; lng: number }[];
}

interface TimelineItem {
  segments: Segment[];
}

/** Constants */
const MAX_ZOOM_LEVEL = 21;
const MIN_ZOOM_LEVEL = 3;
const DEFAULT_ZOOM = 17;

interface DispatchDetailsPopupProps extends AlertPopupProps {
  itineraryDispatchId?: number;
}

const DispatchDetailsPopup: React.FC<DispatchDetailsPopupProps> = ({
  isOpen,
  onClose,
  itineraryDispatchId,
}) => {
  const { t } = useTranslation();
  const [mapAdapter, setMapAdapter] = useState<GeneralMapAdapter | null>(null);

  /** Mock data (replace with API later) */
  const vehicleLocation = useMemo(() => ({ lat: 37.5665, lng: 126.978 }), []);

  const itineraryData = useMemo(
    () => [
      { label: 'ItineraryTitle', value: 'Itinerary 1' },
      { label: 'ItineraryDate', value: '2025-12-12' },
    ],
    [t],
  );

  const vehicleData = useMemo(
    () => [
      { label: 'ModelName', value: '25B-X' },
      { label: 'VehicleNumber', value: 'ABC-1234' },
    ],
    [t],
  );

  const driverData = useMemo(
    () => [
      { label: 'DriverName', value: 'Jason Miller' },
      { label: 'PhoneNumber', value: '+820112341234' },
    ],
    [t],
  );

  const timelineList: TimelineItem[] = useMemo(
    () => [
      {
        segments: [
          {
            tagName: t('StartPoint'),
            address: '서울',
            arrivalTime: '08:00',
            estimatedTime: '08:05',
            completed: true,
            path: [
              { lat: 37.5665, lng: 126.978 },
              { lat: 37.5651, lng: 126.9895 },
            ],
          },
          {
            tagName: t('EndPoint'),
            address: '인천',
            arrivalTime: '09:00',
            estimatedTime: '09:10',
            completed: false,
            path: [
              { lat: 37.5651, lng: 126.9895 },
              { lat: 37.4563, lng: 126.7052 },
            ],
          },
        ],
      },
    ],
    [t],
  );

  /** API **/
  const [dispatchDetail, setDispatchDetail] = useState<AdminItineraryDispatchApiGetAdminItineraryDispatchRequest | null>(null);

  useEffect(() => {
    // TODO: API 호출
    if (itineraryDispatchId) {
      setDispatchDetail.itineraryDispatchId = itineraryDispatchId;
    }
  }, [itineraryDispatchId]);

    /** useQuery */
  const { data: dispatchHistoryPage } = useQuery<{DispatchHistoryPage} | null>({
    queryKey: ['/api/admin/itinerary-dispatch/detail', setDispatchDetail],
    queryFn: async () => {
      try {
        const result = await dispatchApi.getAdminItineraryDispatch(
          setDispatchDetail,
        );
        return {
        };
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    enabled: !!dispatchHistoryParams,
  });

  /** Handlers */
  const handleMapInit = useCallback((adapter: GeneralMapAdapter) => {
    setMapAdapter(adapter);
  }, []);

  const zoomIn = useCallback(() => mapAdapter?.zoomIn(), [mapAdapter]);
  const zoomOut = useCallback(() => mapAdapter?.zoomOut(), [mapAdapter]);

  /** Render */
  return (
    <Layout isOpen={isOpen}>
      <section className="w-[1450px] popup-wrap">
        <article>
          <h2>{t('DispatchDetails')}</h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        <article>
          {/* 데이터 */}
          <div className="f-s-b mb-10">
            <div className="space-y-[10px] [&>div]:f-c [&>div]:gap-10 [&_h2]:w-[150px] [&_h2]:subtitle4">
              <div>
                <h2>{t('ItineraryInformation')}</h2>
                <SummaryData details={itineraryData} fs="lg" />
              </div>
              <div>
                <h2>{t('VehicleInformation')}</h2>
                <SummaryData details={vehicleData} fs="lg" />
              </div>
              <div>
                <h2>{t('DriverInformation')}</h2>
                <SummaryData details={driverData} fs="lg" />
              </div>
            </div>
            <Button variant="bt_tertiary" label={t('Refresh')} />
          </div>

          {/* 지도 */}
          <div className="mb-[30px] h-full relative">
            <GeneralMap
              mapSource={MapEngine.source()}
              className="w-full h-[300px]"
              id="location-general-map"
              maxZoom={MAX_ZOOM_LEVEL}
              minZoom={MIN_ZOOM_LEVEL}
              defaultZoom={DEFAULT_ZOOM}
              onInitMap={handleMapInit}
            >
              {/* 경로 라인 및 마커 */}
              {timelineList.flatMap((item, idx2) =>
                item.segments.map((segment, idx3) => (
                  <React.Fragment key={`seg-${idx2}-${idx3}`}>
                    {segment.path.length > 1 && (
                      <GeneralPolyline
                        path={segment.path}
                        width={10}
                        color={segment.completed ? '#FF5900' : '#A5A5A5'}
                      />
                    )}
                    <TrackingMarker
                      id={`tracking-m-${idx2}-${idx3}`}
                      latlng={segment.path[segment.path.length - 1]}
                      deliveryStatus={segment.completed ? 'after' : 'before'}
                    />
                  </React.Fragment>
                )),
              )}

              <EqSingleMarker id="marker-popup" latlng={vehicleLocation} />
              <EqPositionInfoWindow
                id="info1-popup"
                position={vehicleLocation}
                pixelOffset={[0, -4]}
                routeInfo={{ date: '2024-07-21' }}
              />

              <ZoomController
                right="right-5"
                bottom="bottom-5"
                plus={zoomIn}
                minus={zoomOut}
              />
            </GeneralMap>

            {/* 타임라인 */}
            <div className="w-full f-c border-b border-x border-gray-6">
              <div className="w-full">
                <div className="timeline-wk py-5">
                  <div className="max-h-[250px] card-wrap overflow-x-scroll">
                    {timelineList.map((item, idx2) => (
                      <div className="card-section" key={`tl-${idx2}`}>
                        <div className="time-con">
                          <div className="info time-info">
                            {item.segments.map((seg, sidx) => {
                              const isArrived = sidx < item.segments.length - 1;
                              return (
                                <div
                                  key={`tl-${idx2}-${sidx}`}
                                  className="time-bar"
                                  data-estimatedtime={seg.estimatedTime}
                                >
                                  <TimelineBadge arrived={isArrived} />
                                  <h2>{seg.tagName}</h2>
                                  <p>{seg.address}</p>
                                  <span>{seg.arrivalTime}</span>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 닫기 버튼 */}
          <div className="f-je">
            <Button
              variant="bt_secondary"
              label={t('Cancel')}
              onClick={onClose}
            />
          </div>
        </article>
      </section>
    </Layout>
  );
};

export default DispatchDetailsPopup;
