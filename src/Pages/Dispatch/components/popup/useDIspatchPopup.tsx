import { useTranslation } from 'react-i18next';
import { useToast } from '@/Common/useToast.tsx';
import { useOverlay } from '@toss/use-overlay';
import SearchVehiclePopup from './SearchVehiclePopup.tsx';
import DispatchDetailsPopup from './DispatchDetailsPopup.tsx';

const UseNoticePopup = () => {
  const { t } = useTranslation();

  const { toast } = useToast();

  const overlay = useOverlay();

  const openSearchVehiclePopup = (
    setOption: (option: { key: string; value: string } | null) => void,
    onConfirm: () => void,
  ) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <SearchVehiclePopup
          isOpen={isOpen}
          onClose={close}
          setOption={setOption}
          onConfirm={() => {
            onConfirm();
            // toast({
            //   types: 'success',
            //   description: t('TheNewEquipmentHasBeenRegistered'),
            // });
            close();
          }}
        />
      );
    });
  };

  const openDispatchDetails = (onConfirm: (close: () => void) => void) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <DispatchDetailsPopup
          onClose={close}
          onConfirm={() => {
            onConfirm(close);
            toast({
              types: 'success',
              description: t('TheNewEquipmentHasBeenRegistered'),
            });
            close();
          }}
          isOpen={isOpen}
        />
      );
    });
  };

  return {
    openSearchVehiclePopup,
    openDispatchDetails,
  };
};

export default UseNoticePopup;
