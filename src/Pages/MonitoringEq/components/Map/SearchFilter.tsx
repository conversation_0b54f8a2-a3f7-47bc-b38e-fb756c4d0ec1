import React from 'react';
import VehicleSearchFilter from './VehicleSearchFilter';
import TrackingSearchFilter from './TrackingSearchFilter';
import { EquipmentType } from '@/types/EquipmentType';

interface SearchFilterProps {
  mode?: 'vehicle' | 'tracking';
  left?: string;
  style?: React.CSSProperties;
  onResult: (result: EquipmentType.FilteredMapItem[]) => void;
}

/**
 * 장비 필터 조회
 */
const SearchFilter: React.FC<SearchFilterProps> = ({
  mode,
  left,
  style,
  onResult,
}) => {
  if (mode === 'tracking') {
    return (
      <TrackingSearchFilter left={left} style={style} onResult={onResult} />
    );
  } else {
    return (
      <VehicleSearchFilter left={left} style={style} onResult={onResult} />
    );
  }
};
SearchFilter.displayName = 'SearchFilter';
export default SearchFilter;
