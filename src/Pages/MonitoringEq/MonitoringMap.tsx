import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useLayoutStore } from '@/store/layout.ts';
import { AnimatePresence } from 'framer-motion';
import {
  EqGroupMarkerSet,
  EqSingleMarkerSet,
  MapHelper,
} from '@/Common/constants/Maps.ts';
import { GeneralMap, GeneralMapAdapter } from '@/logiMaps/react/general/Map';
import { MapEngine } from '@/types';
import MonitoringViewListButton from '@/Common/Components/map/MonitoringViewListButton.tsx';
import VehicleViewList from '@/Pages/MonitoringEq/components/Map/VehicleViewList.tsx';
import TrackingViewList from '@/Pages/MonitoringEq/components/Map/TrackingViewList.tsx';
import Timeline from '@/Pages/MonitoringEq/components/Map/Timeline.tsx';
import SearchFilter from '@/Pages/MonitoringEq/components/Map/SearchFilter.tsx';
import EqGroupInfoWindow, {
  EqGroupInfoWindowProps,
} from '@/Common/Components/eqWindow/EqGroupInfoWindow.tsx';
import EqSingleInfoWindow, {
  EqSingleInfoWindowProps,
} from '@/Common/Components/eqWindow/EqSingleInfoWindow.tsx';
import EqGroupMarker, {
  EqGroupMarkerProps,
} from '@/Common/Components/Marker/EqGroupMarker';
import EqSingleMarker, {
  EqSingleMarkerProps,
} from '@/Common/Components/Marker/EqSingleMarker';
import MapModeSwitcher from './components/Map/MapModeSwitcher.tsx';
import ZoomController from '@/Common/Components/map/ZoomController.tsx';
import VehicleDetail from './components/Map/VehicleDetail.tsx';
import TrackingDetail from './components/Map/TrackingDetail.tsx';
import GeneralPolyline from '@/logiMaps/react/general/Poly/GeneralPolyline.tsx';
import { EquipmentType } from '@/types/EquipmentType.ts';
import TrackingMarker from '@/Common/Components/Marker/TrackingMarker.tsx';

/** Types */
type LatLng = { lat: number; lng: number };

type MarkerMode = 0 | 1 | 2; // 0: group, 1~2: single

/** Constants */
const MAX_ZOOM_LEVEL = 18;
const MIN_ZOOM_LEVEL = 4;
const DEFAULT_ZOOM = 15;
const GROUP_OVERLAP_DIST = 100.0;
const SINGLE_OVERLAP_DIST = 1.0; // boundary 검사 안함 (최소 거리만 체크)
const NORTH_AMERICA_BOUNDS = {
  minLat: 10.0,
  maxLat: 82.0,
  minLng: -168.0,
  maxLng: -40.0,
};
const VEHICLE_VIEW_LIST_WIDTH = 412;

/**
 * 모니터링
 */
const MonitoringMap: React.FC = () => {
  const { move: layoutMove } = useLayoutStore((state) => state);

  // Map
  const [mapAdapter, setMapAdapter] = useState<GeneralMapAdapter | null>(null);
  const zoomLevelRef = useRef<number>(DEFAULT_ZOOM);
  const [_, setZoomGauge] = useState<number>(DEFAULT_ZOOM); // for external gauge component if needed

  // Marker state refs
  const markerTypeRef = useRef<MarkerMode>(1);
  const eqDataLevelRef = useRef<number>(0); // 현재 마커 데이터의 기준 레벨 (0.5 step)

  // Data
  const [filteredMapItems, setFilteredMapItems] = useState<
    EquipmentType.FilteredMapItem[]
  >([]);

  // Singles
  const [eqSingleMarkerSets] = useState<Map<number, EqSingleMarkerSet>>(
    new Map(),
  );
  const [eqSingleMarkerProps, setEqSingleMarkerProps] = useState<
    EqSingleMarkerProps[]
  >([]);
  const [eqSingleInfoWindowProps, setEqSingleInfoWindowProps] = useState<
    EqSingleInfoWindowProps | undefined
  >();

  // Groups
  const [eqGroupMarkerSets] = useState<Map<number, EqGroupMarkerSet>>(
    new Map(),
  );
  const [eqGroupMarkerProps, setEqGroupMarkerProps] = useState<
    EqGroupMarkerProps[]
  >([]);
  const [eqGroupInfoWindowProps, setEqGroupInfoWindowProps] = useState<
    EqGroupInfoWindowProps | undefined
  >();

  // Details / tracking
  const [detailInfoItem, setDetailInfoItem] =
    useState<EquipmentType.FilteredMapItem | null>(null);
  const [trackingPath, setTrackingPath] = useState<
    EquipmentType.DispatchInfo[]
  >([]);
  const [mode, setMode] = useState<'vehicle' | 'tracking'>('vehicle');
  const [timelineHeight, setTimelineHeight] = useState(0);

  /** Memoized derived values */
  const inRegionBounds = useMemo<LatLng[]>(() => {
    return filteredMapItems
      .map((i) => i.latlng)
      .filter(
        (ll) =>
          ll.lat >= NORTH_AMERICA_BOUNDS.minLat &&
          ll.lat <= NORTH_AMERICA_BOUNDS.maxLat &&
          ll.lng >= NORTH_AMERICA_BOUNDS.minLng &&
          ll.lng <= NORTH_AMERICA_BOUNDS.maxLng,
      );
  }, [filteredMapItems]);

  /** Effects */
  // 지도 + 데이터 변경 시: bounds fit & 마커 업데이트
  useEffect(() => {
    if (!mapAdapter) return;

    if (filteredMapItems.length > 0 && inRegionBounds.length > 0) {
      fitBounds(inRegionBounds);
    }

    // invalidate caches and redraw
    eqSingleMarkerSets.clear();
    eqGroupMarkerSets.clear();
    updateEqMarkers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mapAdapter, filteredMapItems, inRegionBounds]);

  // 모드 변경 시 모든 창 닫기
  useEffect(() => {
    closeAll();
  }, [mode]);

  // 트래킹 모드에서 path 변화 시 해당 경로로 줌
  useEffect(() => {
    if (!mapAdapter || mode !== 'tracking') return;
    trackingPath.forEach((p) => {
      if (p.path?.length) {
        mapAdapter.fitBounds(p.path, {
          left: '0',
          right: '0',
          top: '0',
          bottom: '0',
        });
      }
    });
  }, [mode, trackingPath, mapAdapter]);

  /** Handlers */
  const onResult = useCallback((result: EquipmentType.FilteredMapItem[]) => {
    setFilteredMapItems(result);
  }, []);

  const handleMapInit = useCallback((adapter: GeneralMapAdapter) => {
    setMapAdapter(adapter);
  }, []);

  const handleMapClick = useCallback(() => {
    setEqSingleInfoWindowProps(undefined);
    setEqGroupInfoWindowProps(undefined);
    closeAll();
  }, []);

  const handleMapZoomChanged = useCallback((zoom: number) => {
    zoomLevelRef.current = zoom;
    setZoomGauge(zoom);
    updateEqMarkers();
  }, []);

  const handleMapBoundsChanged = useCallback(() => {
    updateEqMarkers();
  }, []);

  const handleClickSingleMark = useCallback(
    (id: string, latlng: LatLng) => {
      useLayoutStore.setState({ move: false });
      setEqGroupInfoWindowProps(undefined);

      const item = filteredMapItems.find((d) => d.id === id);
      if (!item) return;

      setEqSingleInfoWindowProps({
        item,
        position: latlng,
        onClose: handleCloseSingleInfoWindow,
      });
    },
    [filteredMapItems],
  );

  const handleClickGroupMark = useCallback(
    (id: string, latlng: LatLng, markers: { id: string; latlng: LatLng }[]) => {
      setEqSingleInfoWindowProps(undefined);

      const items = markers
        .map((m) => filteredMapItems.find((d) => d.id === m.id))
        .filter((r): r is EquipmentType.FilteredMapItem => Boolean(r))
        .map((r) => ({
          id: r.id,
          equipmentId: r.equipmentId,
          modelName: r.modelName,
          vehicleNum: r.plateNo,
          mileage: r.mileage.toString(),
          status: '', //String(r.eqStat),
        }));

      setEqGroupInfoWindowProps({
        id,
        position: latlng,
        items,
        onClose: handleCloseGroupInfoWindow,
      });
    },
    [filteredMapItems],
  );

  const handleCloseGroupInfoWindow = useCallback(() => {
    setEqSingleInfoWindowProps(undefined);
    setEqGroupInfoWindowProps(undefined);
  }, []);

  const handleCloseSingleInfoWindow = useCallback(() => {
    setEqSingleInfoWindowProps(undefined);
    setEqGroupInfoWindowProps(undefined);
  }, []);

  /** Utilities */
  const closeAll = useCallback(() => {
    setDetailInfoItem(null);
    setTrackingPath([]);
    setEqSingleInfoWindowProps(undefined);
    setEqGroupInfoWindowProps(undefined);
    useLayoutStore.setState({ move: false });
  }, []);

  const calcMarkerType = (zoom: number): MarkerMode => {
    if (zoom <= 9) return 0; // group
    if (zoom <= 15) return 1; // single
    return 2; // single (dense)
  };

  const currentDataLevel = (zoom: number) => Math.floor(zoom * 2) / 2; // step 0.5

  const updateEqMarkers = useCallback(() => {
    if (!mapAdapter) return;

    setEqSingleInfoWindowProps(undefined);

    const zoom = Math.floor(zoomLevelRef.current);
    const markerType = calcMarkerType(zoom);
    markerTypeRef.current = markerType;

    const dataLevel = currentDataLevel(zoomLevelRef.current);
    if (eqDataLevelRef.current !== dataLevel) {
      eqDataLevelRef.current = dataLevel;
      setEqGroupInfoWindowProps(undefined); // level 변경 시 그룹 정보창 닫기
    }

    if (markerType === 0) {
      updateEqGroupMarkers(dataLevel);
    } else {
      updateEqSingleMarkers(dataLevel);
    }
  }, [mapAdapter]);

  const updateEqSingleMarkers = useCallback(
    (dataLevel: number) => {
      if (!mapAdapter) {
        setEqSingleMarkerProps([]);
        setEqGroupMarkerProps([]);
        return;
      }

      let result = eqSingleMarkerSets.get(dataLevel);
      if (!result) {
        const set = MapHelper.makeEqSingleMarkerSet(
          filteredMapItems,
          mapAdapter,
          dataLevel,
          SINGLE_OVERLAP_DIST,
        );
        eqSingleMarkerSets.set(dataLevel, set);
        result = set;
      }

      const props: EqSingleMarkerProps[] = [];
      for (const row of result.items) {
        if (mapAdapter.isInBoundary(row.latlng)) {
          props.push({
            id: row.id,
            latlng: row.latlng,
            operationStatus: row.operationStatus,
            breakdownStatus: row.breakdownStatus,
            onClick: handleClickSingleMark,
          });
        }
      }

      setEqSingleMarkerProps(props);
      setEqGroupMarkerProps([]);
    },
    [eqSingleMarkerSets, filteredMapItems, handleClickSingleMark, mapAdapter],
  );

  const updateEqGroupMarkers = useCallback(
    (dataLevel: number) => {
      if (!mapAdapter) {
        setEqSingleMarkerProps([]);
        setEqGroupMarkerProps([]);
        return;
      }

      let result = eqGroupMarkerSets.get(dataLevel);
      if (!result) {
        const set = MapHelper.makeEqGroupMarkerSet(
          filteredMapItems,
          mapAdapter,
          dataLevel,
          GROUP_OVERLAP_DIST,
        );
        eqGroupMarkerSets.set(dataLevel, set);
        result = set;
      }

      const props: EqGroupMarkerProps[] = [];
      for (const row of result.items) {
        if (mapAdapter.isInBoundary(row.latlng)) {
          props.push({
            id: row.id,
            latlng: row.latlng,
            markers: row.markers,
            onClick: handleClickGroupMark,
          });
        }
      }

      setEqSingleMarkerProps([]);
      setEqGroupMarkerProps(props);
    },
    [eqGroupMarkerSets, filteredMapItems, handleClickGroupMark, mapAdapter],
  );

  const fitBounds = useCallback(
    (bounds: LatLng[]) => {
      if (!mapAdapter) return;

      const { width, height } = mapAdapter.getScreenSize();
      const basePadding = {
        top: `${height * 0.1}px`,
        right: `${width * 0.1}px`,
        bottom: `${height * 0.1}px`,
        left: `${width * 0.1}px`,
      };

      if (layoutMove) {
        const visibleWidth = width - VEHICLE_VIEW_LIST_WIDTH;
        basePadding.right = `${visibleWidth * 0.1 + VEHICLE_VIEW_LIST_WIDTH}px`;
        basePadding.left = `${visibleWidth * 0.1}px`;
      }

      mapAdapter.fitBounds(bounds, basePadding);
    },
    [layoutMove, mapAdapter],
  );

  /** Render */
  return (
    <div className="w-full h-full">
      {/* 지도 영역 */}
      <GeneralMap
        mapSource={MapEngine.source()}
        className="w-full h-full overflow-hidden"
        id="equipment"
        maxZoom={MAX_ZOOM_LEVEL}
        minZoom={MIN_ZOOM_LEVEL}
        defaultZoom={DEFAULT_ZOOM}
        onInitMap={handleMapInit}
        onClick={handleMapClick}
        onZoomChanged={handleMapZoomChanged}
        onBoundsChanged={handleMapBoundsChanged}
      >
        {markerTypeRef.current === 0 ? (
          <>
            {/* group markers */}
            {eqGroupMarkerProps.map((data) => (
              <EqGroupMarker key={data.id} {...data} />
            ))}
            {eqGroupInfoWindowProps && (
              <EqGroupInfoWindow {...eqGroupInfoWindowProps} />
            )}
          </>
        ) : (
          <>
            {/* single markers */}
            {eqSingleMarkerProps.map((data) => (
              <EqSingleMarker
                key={data.id}
                markerType={markerTypeRef.current}
                {...data}
              />
            ))}
            {eqSingleInfoWindowProps && (
              <EqSingleInfoWindow {...eqSingleInfoWindowProps} />
            )}
          </>
        )}

        {/* tracking overlays */}
        {mode === 'tracking' &&
          trackingPath.map((pathInfo, index) => {
            if (!pathInfo.path?.length) return null;
            return (
              <GeneralPolyline
                key={`tracking-l-${index}`}
                path={pathInfo.path}
                width={3}
                color={pathInfo.completed ? '#FF5900' : '#A5A5A5'}
              />
            );
          })}

        {mode === 'tracking' &&
          trackingPath.map((pathInfo, index) => {
            if (!pathInfo.path?.length) return null;
            return (
              <TrackingMarker
                key={`tracking-m-${index}`}
                id={`tracking-m-${index}`}
                latlng={pathInfo.path[pathInfo.path.length - 1]}
                deliveryStatus={pathInfo.completed ? 'after' : 'before'}
              />
            );
          })}
      </GeneralMap>

      {/* 검색 필터 */}
      <SearchFilter
        mode={mode}
        left={layoutMove ? 'left-[380px]' : 'left-5'}
        onResult={onResult}
        style={{
          bottom:
            mode === 'tracking' && timelineHeight > 0
              ? `${timelineHeight + 12}px`
              : '30px',
        }}
      />

      {/* 줌 컨트롤 */}
      <ZoomController
        right="right-[30px]"
        plus={() => mapAdapter?.zoomIn()}
        minus={() => mapAdapter?.zoomOut()}
        style={{
          bottom:
            mode === 'tracking' && timelineHeight > 0
              ? `${timelineHeight + 12}px`
              : '30px',
        }}
      />

      {/* 지도 상태 변경 */}
      <AnimatePresence>
        <MapModeSwitcher onChange={setMode} />
      </AnimatePresence>

      {mode === 'vehicle' && (
        <>
          {/* 차량 상태 보기 */}
          <MonitoringViewListButton status={layoutMove} />
          <AnimatePresence>
            {layoutMove && (
              <VehicleViewList
                items={filteredMapItems}
                onListItemClick={(item: EquipmentType.FilteredMapItem) => {
                  setDetailInfoItem(item);
                  setTrackingPath([]);
                }}
              />
            )}
          </AnimatePresence>

          {layoutMove && detailInfoItem && (
            <VehicleDetail
              item={detailInfoItem}
              onClose={() => {
                setDetailInfoItem(null);
                setTrackingPath([]);
              }}
            />
          )}
        </>
      )}

      {mode === 'tracking' && (
        <>
          {/* 트래킹 지도 보기 */}
          <MonitoringViewListButton status={layoutMove} />
          <AnimatePresence>
            {layoutMove && !detailInfoItem && (
              <TrackingViewList
                items={filteredMapItems}
                onListItemClick={(item: EquipmentType.FilteredMapItem) => {
                  setDetailInfoItem(item);
                }}
              />
            )}
          </AnimatePresence>

          {layoutMove && detailInfoItem && (
            <TrackingDetail
              item={detailInfoItem}
              onTrackingPath={setTrackingPath}
              onClose={() => {
                setDetailInfoItem(null);
                setTrackingPath([]);
              }}
            />
          )}

          <Timeline
            items={filteredMapItems}
            onHeightChange={setTimelineHeight}
            isChange={layoutMove}
          />
        </>
      )}
    </div>
  );
};

export default MonitoringMap;
